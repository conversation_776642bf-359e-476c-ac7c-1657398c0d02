
import React, { useEffect, useRef } from 'react';
import { useAppContext } from '../contexts/AppContext';
import ProductSearch from '../components/ProductSearch';
import Cart from '../components/Cart';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Plus } from 'lucide-react'; // Removed Barcode import
// Removed setupBarcodeScanner import
import ReceiptPreview from '../components/ReceiptPreview';
import { formatCurrency } from '@/lib/utils';

const Index = () => {
  const { products, addToCart, lastTransaction } = useAppContext();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [activeTab, setActiveTab] = React.useState('all');
  const [showReceipt, setShowReceipt] = React.useState(false);
  const [categories, setCategories] = React.useState<string[]>([]);
  const [filteredProducts, setFilteredProducts] = React.useState(products);

  // Removed barcode scanner setup useEffect hook
  
  // Extract unique categories
  useEffect(() => {
    const uniqueCategories = [...new Set(products.map(p => p.category))];
    setCategories(uniqueCategories);
  }, [products]);
  
  // Filter products by category
  useEffect(() => {
    if (activeTab === 'all') {
      setFilteredProducts(products);
    } else {
      setFilteredProducts(products.filter(p => p.category === activeTab));
    }
  }, [activeTab, products]);
  
  // Show receipt when a transaction is completed
  useEffect(() => {
    if (lastTransaction) {
      setShowReceipt(true);
    }
  }, [lastTransaction]);

  return (
    // Changed grid from 3 columns to 5 columns on md+ screens
    <div className="h-[calc(100vh-70px)] grid grid-cols-1 md:grid-cols-5 gap-4"> 
      {/* Product browsing area (now 3/5 width) */}
      <div className="md:col-span-3 space-y-4"> 
        <Card>
          <CardContent className="pt-4">
            <ProductSearch />
            
            <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
              <TabsList className="mb-4 w-full flex overflow-x-auto">
                <TabsTrigger value="all">All Products</TabsTrigger>
                {categories.map(category => (
                  <TabsTrigger key={category} value={category}>
                    {category}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              <TabsContent value={activeTab} className="mt-0">
                <ScrollArea className="h-[calc(100vh-260px)]">
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 p-1">
                    {filteredProducts.map(product => (
                      <Card 
                        key={product.id} 
                        className="card-hover cursor-pointer overflow-hidden"
                        onClick={() => addToCart(product)}
                      >
                        <div className="relative h-32 bg-muted flex items-center justify-center">
                          {product.image ? (
                            <img 
                              src={product.image} 
                              alt={product.name} 
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <span className="text-sm text-muted-foreground">No Image</span> // Replaced Barcode icon
                          )}
                          <div className="absolute bottom-2 right-2 bg-white/90 rounded-full px-2 py-1 text-xs font-medium">
                            {formatCurrency(product.unitPrice)}
                          </div>
                        </div>
                        <CardContent className="p-3">
                          <div className="flex flex-col h-full">
                            <h3 className="font-medium text-sm truncate">{product.name}</h3>
                            <p className="text-xs text-muted-foreground truncate">
                              {product.category} • {product.supplierName} {/* Display supplierName */}
                            </p>
                            <div className="flex items-center justify-between mt-2">
                              <span className={`text-xs ${product.stockQuantity <= 15 ? 'text-pharmacy-danger' : 'text-pharmacy-success'}`}>
                                {product.stockQuantity} in stock
                              </span>
                              <Button size="sm" className="h-7 w-7 p-0">
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
      
      {/* Cart area (now 2/5 width) */}
      <div className="md:col-span-2"> 
        <Cart />
      </div>
      
      {/* Receipt Dialog */}
      {lastTransaction && (
        <Dialog open={showReceipt} onOpenChange={setShowReceipt}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Receipt #{lastTransaction.receiptNumber}</DialogTitle>
            </DialogHeader>
            <ReceiptPreview transaction={lastTransaction} />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default Index;
