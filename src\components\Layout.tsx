
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Sidebar, 
  SidebarContent, 
  SidebarFooter, 
  SidebarHeader, 
  SidebarProvider, 
  SidebarTrigger,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton
} from "@/components/ui/sidebar";
import {
  ShoppingCart,
  Users,
  Boxes, // Replaced Barcode with Boxes
  FileText,
  BarChart,
  Settings,
  Clock,
  User,
  Receipt
} from "lucide-react";

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const menuItems = [
    { path: '/', label: 'Point of Sale', icon: ShoppingCart },
    { path: '/todays-sales', label: "Today's Sales", icon: BarChart }, // Added Today's Sales
    { path: '/inventory', label: 'Inventory', icon: Boxes }, // Replaced Barcode with Boxes
    // Removed Customers link
    { path: '/expenses', label: 'Expenses', icon: Receipt }, // Added Expenses
    { path: '/reports', label: 'Reports', icon: BarChart },
    { path: '/transactions', label: 'Transactions', icon: FileText },
    { path: '/settings', label: 'Settings', icon: Settings },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <Sidebar className="border-r border-border">
          <SidebarHeader className="px-4 py-3">
            <div className="flex items-center gap-2">
              <div className="bg-pharmacy-primary text-white p-1 rounded">
                <Boxes size={20} /> {/* Replaced Barcode with Boxes */}
              </div>
              <div className="font-bold text-lg text-pharmacy-primary">PharmaPOS</div>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>Main Navigation</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {menuItems.map((item) => (
                    <SidebarMenuItem key={item.path}>
                      <SidebarMenuButton
                        onClick={() => navigate(item.path)}
                        className={isActive(item.path) ? "bg-sidebar-accent" : ""}
                      >
                        <item.icon className={`${isActive(item.path) ? "text-pharmacy-primary" : ""}`} />
                        <span>{item.label}</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>
          <SidebarFooter className="p-4">
            <div className="flex items-center gap-3 px-3">
              <div className="bg-muted rounded-full p-1">
                <User size={16} className="text-muted-foreground" />
              </div>
              <div className="flex-1 truncate">
                <p className="text-sm font-medium">Jane Smith</p>
                <p className="text-xs text-muted-foreground">Pharmacist</p>
              </div>
            </div>
          </SidebarFooter>
        </Sidebar>
        <main className="flex-1 overflow-y-auto">
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-2">
              <SidebarTrigger className="lg:hidden" />
              <h1 className="text-xl font-semibold">
                {menuItems.find(item => item.path === location.pathname)?.label || 'Dashboard'}
              </h1>
            </div>
            <div className="flex items-center gap-4">
              <Clock size={18} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">{new Date().toLocaleDateString()}</span>
            </div>
          </div>
          <div className="p-4">{children}</div>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default Layout;
