
import React, { useState } from 'react';
import { CartItem } from '../types/models';
import { useAppContext } from '../contexts/AppContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Minus, Plus, X } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface CartItemRowProps {
  item: CartItem;
  index: number;
}

const CartItemRow: React.FC<CartItemRowProps> = ({ item, index }) => {
  const { updateCartItem, removeFromCart } = useAppContext();
  const [quantity, setQuantity] = useState(item.quantity);
  const [discount, setDiscount] = useState(item.discount);

  // Calculate item total
  const subtotal = item.product.unitPrice * item.quantity;
  const discountAmount = subtotal * (item.discount / 100);
  const total = subtotal - discountAmount;

  // Update quantity
  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuantity = parseInt(e.target.value) || 0;
    setQuantity(newQuantity);
    updateCartItem(index, newQuantity, discount);
  };

  // Increment quantity
  const incrementQuantity = () => {
    const newQuantity = quantity + 1;
    setQuantity(newQuantity);
    updateCartItem(index, newQuantity, discount);
  };

  // Decrement quantity
  const decrementQuantity = () => {
    if (quantity > 1) {
      const newQuantity = quantity - 1;
      setQuantity(newQuantity);
      updateCartItem(index, newQuantity, discount);
    }
  };

  // Update discount
  const handleDiscountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDiscount = parseFloat(e.target.value) || 0;
    setDiscount(newDiscount);
    updateCartItem(index, quantity, newDiscount);
  };

  return (
    <div className="py-3 px-2 border-b bg-white hover:bg-gray-50 transition-colors">
      {/* Product Information Row - Always visible and prominent */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0 pr-4">
          <h3 className="font-medium text-base text-gray-900 leading-tight mb-1">
            {item.product.name}
          </h3>
          <p className="text-sm text-gray-600">
            {formatCurrency(item.product.unitPrice)} per unit
          </p>
        </div>

        {/* Remove button - positioned at top right */}
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 text-gray-400 hover:text-red-500 hover:bg-red-50 flex-shrink-0"
          onClick={() => removeFromCart(index)}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Controls Row - Separated from product name */}
      <div className="flex items-center justify-between gap-3">
        {/* Quantity Controls */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 font-medium">Qty:</span>
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7 border-gray-300"
              onClick={decrementQuantity}
            >
              <Minus className="h-3 w-3" />
            </Button>
            <Input
              type="number"
              min="1"
              value={quantity}
              onChange={handleQuantityChange}
              className="w-14 h-7 text-center text-sm border-gray-300"
            />
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7 border-gray-300"
              onClick={incrementQuantity}
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Discount Input */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 font-medium">Disc:</span>
          <Input
            type="number"
            min="0"
            max="100"
            value={discount}
            onChange={handleDiscountChange}
            className="w-16 h-7 text-sm border-gray-300"
            placeholder="0%"
          />
        </div>

        {/* Item Total */}
        <div className="text-right">
          <div className="text-sm text-gray-600">Total:</div>
          <div className="font-semibold text-base text-gray-900">
            {formatCurrency(total)}
          </div>
        </div>
      </div>

      {/* Summary line showing calculation */}
      <div className="mt-2 text-xs text-gray-500">
        {formatCurrency(item.product.unitPrice)} × {item.quantity}
        {item.discount > 0 && ` (-${item.discount}%)`}
      </div>
    </div>
  );
};

export default CartItemRow;
