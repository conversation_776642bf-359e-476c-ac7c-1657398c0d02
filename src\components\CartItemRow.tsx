
import React, { useState } from 'react';
import { CartItem } from '../types/models';
import { useAppContext } from '../contexts/AppContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Minus, Plus, X } from 'lucide-react';

interface CartItemRowProps {
  item: CartItem;
  index: number;
}

const CartItemRow: React.FC<CartItemRowProps> = ({ item, index }) => {
  const { updateCartItem, removeFromCart } = useAppContext();
  const [quantity, setQuantity] = useState(item.quantity);
  const [discount, setDiscount] = useState(item.discount);

  // Calculate item total
  const subtotal = item.product.unitPrice * item.quantity;
  const discountAmount = subtotal * (item.discount / 100);
  const total = subtotal - discountAmount;

  // Update quantity
  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuantity = parseInt(e.target.value) || 0;
    setQuantity(newQuantity);
    updateCartItem(index, newQuantity, discount);
  };

  // Increment quantity
  const incrementQuantity = () => {
    const newQuantity = quantity + 1;
    setQuantity(newQuantity);
    updateCartItem(index, newQuantity, discount);
  };

  // Decrement quantity
  const decrementQuantity = () => {
    if (quantity > 1) {
      const newQuantity = quantity - 1;
      setQuantity(newQuantity);
      updateCartItem(index, newQuantity, discount);
    }
  };

  // Update discount
  const handleDiscountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDiscount = parseFloat(e.target.value) || 0;
    setDiscount(newDiscount);
    updateCartItem(index, quantity, newDiscount);
  };

  return (
    // Added justify-between to the main flex container
    <div className="flex items-center justify-between py-2 border-b"> 
      {/* Restored flex-1 and added min-w-0 to allow shrinking */}
      <div className="flex-1 min-w-0"> 
        {/* Removed truncate class */}
        {/* Changed font size from default to text-sm */}
        <p className="font-medium text-sm">{item.product.name}</p> 
        {/* Changed secondary text size from text-sm to text-xs */}
        <p className="text-xs text-muted-foreground"> 
          ${item.product.unitPrice.toFixed(2)} × {item.quantity}
          {item.discount > 0 && ` (-${item.discount}%)`}
        </p>
      </div>
      
      {/* Removed margin (ml-2) and min-w-fit, kept space-x-1, flex-shrink-0 */}
      <div className="flex items-center space-x-1 flex-shrink-0"> 
        <div className="flex items-center space-x-1">
          {/* Reduced button size from h-8 w-8 to h-6 w-6 */}
          <Button 
            variant="outline" 
            size="icon" 
            className="h-6 w-6" 
            onClick={decrementQuantity}
          >
            <Minus className="h-3 w-3" />
          </Button>
          <Input
            type="number"
            min="1"
            value={quantity}
            onChange={handleQuantityChange}
            // Removed misplaced comment
            className="w-12 h-6 text-center text-sm" 
          />
          {/* Reduced button size from h-8 w-8 to h-6 w-6 */}
          <Button 
            variant="outline" 
            size="icon" 
            className="h-6 w-6" 
            onClick={incrementQuantity}
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>
        
        {/* Restoring discount input */}
        <Input
          type="number"
          min="0" max="100" 
            value={discount}
            onChange={handleDiscountChange}
            // Removed fixed width w-12
            className="h-6 text-sm" 
            placeholder="Disc %"
        />
        
        {/* Removed fixed width w-14 */}
        <div className="text-right text-sm">${total.toFixed(2)}</div> 
        
        {/* Reduced button size from h-8 w-8 to h-6 w-6 */}
        <Button 
          variant="ghost" 
          size="icon"
          className="h-6 w-6 text-muted-foreground hover:text-destructive" 
          onClick={() => removeFromCart(index)}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default CartItemRow;
