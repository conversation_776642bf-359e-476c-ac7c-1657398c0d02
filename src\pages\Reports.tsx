
import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { BarChart, FileDown, Printer } from 'lucide-react';
import { Bar, BarChart as RechartsBarChart, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, eachDayOfInterval, eachWeekOfInterval, eachMonthOfInterval, subMonths } from 'date-fns';
import { useAppContext } from '../contexts/AppContext';
import { formatCurrency } from '@/lib/utils';

const Reports = () => {
  const { transactions = [] } = useAppContext();
  const [startDate, setStartDate] = useState('2025-04-20');
  const [endDate, setEndDate] = useState('2025-04-27');
  const [viewType, setViewType] = useState<'daily' | 'weekly' | 'monthly'>('daily');

  // Calculate profits based on date range and view type
  const profitData = useMemo(() => {
    if (!transactions || transactions.length === 0) {
      return [];
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    let intervals: Date[];

    switch (viewType) {
      case 'daily':
        intervals = eachDayOfInterval({ start, end });
        break;
      case 'weekly':
        intervals = eachWeekOfInterval({ start, end });
        break;
      case 'monthly':
        intervals = eachMonthOfInterval({ start: subMonths(start, 1), end });
        break;
      default:
        intervals = eachDayOfInterval({ start, end });
    }

    return intervals.map(date => {
      const dateStr = format(date, 'yyyy-MM-dd');
      const periodTransactions = transactions.filter(t => {
        const txDate = new Date(t.dateTime);
        switch (viewType) {
          case 'daily':
            return format(txDate, 'yyyy-MM-dd') === dateStr;
          case 'weekly':
            return txDate >= startOfWeek(date) && txDate < endOfWeek(date);
          case 'monthly':
            return txDate >= startOfMonth(date) && txDate < endOfMonth(date);
          default:
            return false;
        }
      });

      const profit = periodTransactions.reduce((sum, t) => sum + (t.profit || 0), 0);
      const sales = periodTransactions.reduce((sum, t) => sum + t.total, 0);

      return {
        name: format(date, viewType === 'daily' ? 'MMM dd' : viewType === 'weekly' ? 'MMM dd w' : 'MMM yyyy'),
        profit,
        sales,
      };
    });
  }, [transactions, startDate, endDate, viewType]);

  const totalProfit = profitData.reduce((sum, d) => sum + d.profit, 0);
  const totalSales = profitData.reduce((sum, d) => sum + d.sales, 0);

  const handleGenerateReport = () => {
    // In a real app, this would fetch data for the specified date range
    console.log(`Generating report from ${startDate} to ${endDate}`);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    // In a real app, this would generate a CSV or PDF report
    alert('In a real app, this would download a CSV or PDF report.');
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="flex items-center">
              <BarChart className="h-5 w-5 mr-2" />
              Sales & Profit Reports
            </CardTitle>
            <div className="flex flex-wrap items-center gap-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="view-type">View:</Label>
                <select
                  id="view-type"
                  value={viewType}
                  onChange={(e) => setViewType(e.target.value as 'daily' | 'weekly' | 'monthly')}
                  className="border rounded px-2 py-1"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
              <div className="flex items-center space-x-2">
                <Label htmlFor="start-date">Start Date:</Label>
                <Input
                  id="start-date"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-auto"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Label htmlFor="end-date">End Date:</Label>
                <Input
                  id="end-date"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-auto"
                />
              </div>
              <Button onClick={handleGenerateReport}>Generate</Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <h3 className="text-lg font-medium">
              Profit & Sales Report: {format(new Date(startDate), 'MMM dd, yyyy')} - {format(new Date(endDate), 'MMM dd, yyyy')}
            </h3>
            <p className="text-sm text-muted-foreground">
              Total Profit: {formatCurrency(totalProfit)} | Total Sales: {formatCurrency(totalSales)} | Margin: {((totalProfit / totalSales) * 100).toFixed(1)}%
            </p>
          </div>
          
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart
                data={profitData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip
                  formatter={(value) => [`K${Number(value).toFixed(2)}`, 'Amount']}
                  labelFormatter={(label) => `Date: ${label}`}
                />
                <Legend />
                <Bar dataKey="sales" name="Sales (K)" fill="#2D7AF6" />
                <Bar dataKey="profit" name="Profit (K)" fill="#20D9D2" />
              </RechartsBarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="py-4">
            <CardTitle>Total Sales</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-pharmacy-primary">{formatCurrency(totalSales)}</div>
            <p className="text-sm text-muted-foreground">
              For period {format(new Date(startDate), 'MMM dd, yyyy')} - {format(new Date(endDate), 'MMM dd, yyyy')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="py-4">
            <CardTitle>Total Profit</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-pharmacy-secondary">{formatCurrency(totalProfit)}</div>
            <p className="text-sm text-muted-foreground">
              For period {format(new Date(startDate), 'MMM dd, yyyy')} - {format(new Date(endDate), 'MMM dd, yyyy')}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="py-4">
            <CardTitle>Profit Margin</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-pharmacy-accent">{((totalProfit / totalSales) * 100).toFixed(1)}%</div>
            <p className="text-sm text-muted-foreground">
              Based on sales and profit for the selected period
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Reports;
