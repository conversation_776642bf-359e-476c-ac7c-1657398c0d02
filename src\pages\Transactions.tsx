import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useAppContext } from '../contexts/AppContext';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { save } from '../services/storageService';
import { Product } from '../types/models';
import { formatCurrency } from '@/lib/utils';

const Transactions = () => {
  const { transactions } = useAppContext();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTransactions, setFilteredTransactions] = useState(transactions);

  useEffect(() => {
    // Function to filter transactions based on search query
    const filterTransactions = () => {
      if (!searchQuery) {
        setFilteredTransactions(transactions);
        return;
      }

      const filtered = transactions.filter(transaction => {
        // Convert transaction and search query to lowercase for case-insensitive search
        const transactionData = JSON.stringify(transaction).toLowerCase();
        const query = searchQuery.toLowerCase();

        return transactionData.includes(query);
      });

      setFilteredTransactions(filtered);
    };

    filterTransactions();
  }, [searchQuery, transactions]);

  // Demo data creation functions
  const createDemoData = async () => {
    await createProduct1();
    await createProduct2();
    await createProduct3();
    await createProduct4();
    await createProduct5();
    alert('Demo products created. Please refresh the page.');
  };

  const createProduct1 = async () => {
    const product: Product = {
      id: 101,
      // barcode: '8901234567890', // Removed barcode
      name: 'Paracetamol 500mg',
      category: 'Pain Relief',
      supplierName: 'PharmaCo', // Assuming a supplier name
      buyingPrice: 0.20,
      unitPrice: 0.50,
      taxRate: 5,
      stockQuantity: 1000,
      expiryDate: '2026-12-31',
      batchNumber: 'PCM-2023-001'
    };
    await save<Product>('products', product);
  };

  const createProduct2 = async () => {
    const product: Product = {
      id: 102,
      // barcode: '8909876543210', // Removed barcode
      name: 'Amoxicillin 250mg',
      category: 'Antibiotics',
      supplierName: 'MediSupply', // Assuming a supplier name
      buyingPrice: 0.15,
      unitPrice: 0.40,
      taxRate: 5,
      stockQuantity: 800,
      expiryDate: '2025-11-30',
      batchNumber: 'AMX-2023-002'
    };
    await save<Product>('products', product);
  };

  const createProduct3 = async () => {
    const product: Product = {
      id: 103,
      // barcode: '8905555555555', // Removed barcode
      name: 'Loratadine 10mg',
      category: 'Antihistamines',
      supplierName: 'HealthInc', // Assuming a supplier name
      buyingPrice: 0.10,
      unitPrice: 0.30,
      taxRate: 5,
      stockQuantity: 1200,
      expiryDate: '2027-06-30',
      batchNumber: 'LTD-2023-003'
    };
    await save<Product>('products', product);
  };

  const createProduct4 = async () => {
    const product: Product = {
      id: 104,
      // barcode: '8901111111111', // Removed barcode
      name: 'Omeprazole 20mg',
      category: 'Gastrointestinal',
      supplierName: 'MediSupply', // Assuming a supplier name
      buyingPrice: 0.25,
      unitPrice: 0.60,
      taxRate: 5,
      stockQuantity: 900,
      expiryDate: '2026-09-30',
      batchNumber: 'OMP-2023-004'
    };
    await save<Product>('products', product);
  };

  const createProduct5 = async () => {
    const product: Product = {
      id: 105,
      // barcode: '8907777777777', // Removed barcode
      name: 'Vitamin D3 1000IU',
      category: 'Vitamins & Supplements',
      supplierName: 'PharmaCo', // Assuming a supplier name
      buyingPrice: 0.30,
      unitPrice: 0.75,
      taxRate: 5,
      stockQuantity: 1100,
      expiryDate: '2028-03-31',
      batchNumber: 'VIT-2023-005'
    };
    await save<Product>('products', product);
  };

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <Button onClick={createDemoData}>Create Demo Products</Button>
          </div>
          <div className="mb-4">
            <Input
              type="text"
              placeholder="Search transactions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">Receipt</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Cashier</TableHead>
                  {/* Removed Customer header */}
                  <TableHead>Total</TableHead>
                  <TableHead>Payments</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTransactions && filteredTransactions.length > 0 ? (
                  filteredTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">{transaction.receiptNumber}</TableCell>
                      <TableCell>{format(new Date(transaction.dateTime), 'yyyy-MM-dd HH:mm')}</TableCell>
                      <TableCell>{transaction.cashierName}</TableCell>
                      {/* Removed Customer cell */}
                      <TableCell>{formatCurrency(transaction.total)}</TableCell>
                      <TableCell>
                        {transaction.payments.map((payment, index) => (
                          <div key={index}>
                            {payment.method}: {formatCurrency(payment.amount)}
                          </div>
                        ))}
                      </TableCell>
                      <TableCell>{transaction.status}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    {/* Updated colSpan from 7 to 6 */}
                    <TableCell colSpan={6} className="text-center">No transactions found.</TableCell> 
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Transactions;
