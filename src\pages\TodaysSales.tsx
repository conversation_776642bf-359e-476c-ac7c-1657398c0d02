import React, { useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useAppContext } from '../contexts/AppContext';
import { format, isToday } from 'date-fns';
import { CartItem } from '@/types/models'; // Assuming CartItem includes price info or can be derived
import { formatCurrency } from '@/lib/utils';

interface AggregatedSalesItem {
    productId: number;
    name: string;
    quantitySold: number;
    totalRevenue: number;
    unitPrice: number; // Displaying the price used in calculation
}

const TodaysSales = () => {
    const { transactions = [] } = useAppContext();

    const today = new Date();

    const todaysSalesData = useMemo(() => {
        // Filter transactions for today and ensure they are completed
        const todaysTransactions = transactions.filter(t => 
            isToday(new Date(t.dateTime)) && t.status === 'completed'
        );

        // Aggregate sales data per product
        const aggregatedItems: Record<number, AggregatedSalesItem> = {};

        todaysTransactions.forEach(transaction => {
            transaction.items.forEach(item => {
                const productId = item.product.id;
                // Determine the effective price (override or unit price)
                const price = item.override?.price ?? item.product.unitPrice; 
                // Calculate revenue for this item instance, considering quantity and discount
                const revenue = price * item.quantity * (1 - (item.discount || 0) / 100); 

                if (aggregatedItems[productId]) {
                    // If product already exists in aggregation, update totals
                    aggregatedItems[productId].quantitySold += item.quantity;
                    aggregatedItems[productId].totalRevenue += revenue;
                    // Note: unitPrice remains from the first encountered item for simplicity in display
                } else {
                    // If new product, add it to the aggregation
                    aggregatedItems[productId] = {
                        productId: productId,
                        name: item.product.name,
                        quantitySold: item.quantity,
                        totalRevenue: revenue,
                        unitPrice: price, // Store the price used for display
                    };
                }
            });
        });

        // Convert the aggregated map to an array for rendering
        return Object.values(aggregatedItems);
    }, [transactions]);

    // Calculate the total revenue for the day from the aggregated items
    const totalRevenueToday = useMemo(() => {
        return todaysSalesData.reduce((sum, item) => sum + item.totalRevenue, 0);
    }, [todaysSalesData]);

    return (
        <div className="container mx-auto py-6">
            <Card className="shadow-lg border border-gray-200 dark:border-gray-700 rounded-lg">
                <CardHeader className="bg-gray-50 dark:bg-gray-800 rounded-t-lg py-4 px-6">
                    <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-100">
                        Today's Sales Report ({format(today, 'MMMM dd, yyyy')})
                    </CardTitle>
                </CardHeader>
                <CardContent className="p-0"> {/* Remove padding to allow table full width */}
                    <Table>
                        <TableCaption className="py-4 text-sm text-gray-500 dark:text-gray-400">
                            A list of products sold today.
                        </TableCaption>
                        <TableHeader className="bg-gray-100 dark:bg-gray-700">
                            <TableRow>
                                <TableHead className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Product Name</TableHead>
                                <TableHead className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Quantity Sold</TableHead>
                                <TableHead className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Unit Price</TableHead>
                                <TableHead className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total Revenue</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            {todaysSalesData.length > 0 ? (
                                todaysSalesData.map((item) => (
                                    <TableRow key={item.productId} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{item.name}</TableCell>
                                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 text-right">{item.quantitySold}</TableCell>
                                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 text-right">{formatCurrency(item.unitPrice)}</TableCell>
                                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 text-right font-medium">{formatCurrency(item.totalRevenue)}</TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={4} className="px-6 py-10 text-center text-sm text-gray-500 dark:text-gray-400">
                                        No sales recorded today.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
                <CardFooter className="flex justify-end bg-gray-50 dark:bg-gray-800 rounded-b-lg py-4 px-6 border-t border-gray-200 dark:border-gray-700">
                    <div className="text-lg font-bold text-gray-800 dark:text-gray-100">
                        Total Revenue Today: {formatCurrency(totalRevenueToday)}
                    </div>
                </CardFooter>
            </Card>
        </div>
    );
};

export default TodaysSales;
