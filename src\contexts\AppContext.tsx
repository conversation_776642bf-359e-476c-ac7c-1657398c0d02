import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
// Removed Customer import
import { CartItem, Product, Transaction, Payment, Expense } from '../types/models';
import { initializeStorage, getAll, save } from '../services/storageService';
import { toast } from '@/hooks/use-toast';

interface AppContextType {
  // Cart state
  cart: CartItem[];
  addToCart: (product: Product, quantity?: number) => void;
  updateCartItem: (index: number, quantity: number, discount?: number) => void;
  removeFromCart: (index: number) => void;
  clearCart: () => void;
  
  // Cart calculations
  subtotal: number;
  taxTotal: number;
  discountTotal: number;
  grandTotal: number;
  
  // Customer section removed
  
  // Payment
  payments: Payment[];
  addPayment: (payment: Payment) => void;
  removePayment: (index: number) => void;
  clearPayments: () => void;
  remainingBalance: number;
  
  // Transaction
  completeTransaction: () => Promise<Transaction>;
  lastTransaction: Transaction | null;
  transactions: Transaction[]; // Added transactions array
  
  // Product data
  products: Product[];
  refreshProducts: () => Promise<void>;
  saveProduct: (product: Product) => Promise<void>;

  // Expense data
  expenses: Expense[];
  refreshExpenses: () => Promise<void>;
  saveExpense: (expense: Expense) => Promise<void>;
  deleteExpense: (expenseId: string) => Promise<void>;

  // Customer data section removed

  // Loading state
  isLoading: boolean;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [cart, setCart] = useState<CartItem[]>([]);
  // Removed currentCustomer state
  const [payments, setPayments] = useState<Payment[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  // Removed customers state
  const [transactions, setTransactions] = useState<Transaction[]>([]); // Added transactions state
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [lastTransaction, setLastTransaction] = useState<Transaction | null>(null);
  
  useEffect(() => {
    const init = async () => {
      try {
        await initializeStorage();
        await refreshProducts();
        // Removed refreshCustomers call
        await refreshTransactions(); // Added loading transactions
        await refreshExpenses(); // Added loading expenses
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing app:', error);
        toast({
          title: 'Error',
          description: 'Failed to initialize application data',
          variant: 'destructive'
        });
      }
    };
    
    init();
  }, []);
  
  // Add refreshTransactions function
  const refreshTransactions = async () => {
    try {
      const transactionData = await getAll<Transaction>('transactions');
      setTransactions(transactionData);
    } catch (error) {
      console.error('Error loading transactions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load transactions',
        variant: 'destructive'
      });
    }
  };

  // Add expense management functions
  const refreshExpenses = async () => {
    try {
      const expenseData = await getAll<Expense>('expenses');
      setExpenses(expenseData);
    } catch (error) {
      console.error('Error loading expenses:', error);
      toast({
        title: 'Error',
        description: 'Failed to load expenses',
        variant: 'destructive'
      });
    }
  };

  const saveExpense = async (expense: Expense) => {
    try {
      await save<Expense>('expenses', expense);
      await refreshExpenses();
      toast({
        title: 'Expense saved',
        description: 'Expense has been saved successfully',
      });
      return Promise.resolve();
    } catch (error) {
      console.error('Error saving expense:', error);
      toast({
        title: 'Error',
        description: 'Failed to save expense',
        variant: 'destructive'
      });
      return Promise.reject(error);
    }
  };

  const deleteExpense = async (expenseId: string) => {
    try {
      // Note: This would need a delete function in storageService
      // For now, we'll filter it out from the state
      const updatedExpenses = expenses.filter(expense => expense.id !== expenseId);
      setExpenses(updatedExpenses);
      toast({
        title: 'Expense deleted',
        description: 'Expense has been deleted successfully',
      });
      return Promise.resolve();
    } catch (error) {
      console.error('Error deleting expense:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete expense',
        variant: 'destructive'
      });
      return Promise.reject(error);
    }
  };
  
  const refreshProducts = async () => {
    try {
      const productData = await getAll<Product>('products');
      setProducts(productData);
    } catch (error) {
      console.error('Error loading products:', error);
      toast({
        title: 'Error',
        description: 'Failed to load products',
        variant: 'destructive'
      });
    }
  };
  
  const saveProduct = async (product: Product) => {
    try {
      await save<Product>('products', product);
      await refreshProducts();
      return Promise.resolve();
    } catch (error) {
      console.error('Error saving product:', error);
      return Promise.reject(error);
    }
  };
  
  // Removed refreshCustomers function
  
  const addToCart = (product: Product, quantity = 1) => {
    const existingIndex = cart.findIndex(item => item.product.id === product.id);
    
    if (existingIndex >= 0) {
      const updatedCart = [...cart];
      updatedCart[existingIndex].quantity += quantity;
      setCart(updatedCart);
    } else {
      setCart([...cart, { product, quantity, discount: 0 }]);
    }
    
    toast({
      title: 'Added to cart',
      description: `${product.name} added to cart`,
    });
  };
  
  const updateCartItem = (index: number, quantity: number, discount = 0) => {
    if (index < 0 || index >= cart.length) return;
    
    const updatedCart = [...cart];
    updatedCart[index].quantity = quantity;
    updatedCart[index].discount = discount;
    setCart(updatedCart);
  };
  
  const removeFromCart = (index: number) => {
    if (index < 0 || index >= cart.length) return;
    
    const updatedCart = [...cart];
    const removedItem = updatedCart[index];
    updatedCart.splice(index, 1);
    setCart(updatedCart);
    
    toast({
      title: 'Removed from cart',
      description: `${removedItem.product.name} removed from cart`,
    });
  };
  
  const clearCart = () => {
    setCart([]);
  };
  
  const addPayment = (payment: Payment) => {
    setPayments([...payments, payment]);
  };
  
  const removePayment = (index: number) => {
    if (index < 0 || index >= payments.length) return;
    
    const updatedPayments = [...payments];
    updatedPayments.splice(index, 1);
    setPayments(updatedPayments);
  };
  
  const clearPayments = () => {
    setPayments([]);
  };
  
  const subtotal = cart.reduce((sum, item) => 
    sum + (item.product.unitPrice * item.quantity), 0);
  
  const taxTotal = cart.reduce((sum, item) => {
    const itemSubtotal = item.product.unitPrice * item.quantity;
    const itemDiscount = itemSubtotal * (item.discount / 100);
    return sum + ((itemSubtotal - itemDiscount) * (item.product.taxRate / 100));
  }, 0);
  
  const discountTotal = cart.reduce((sum, item) => {
    const itemSubtotal = item.product.unitPrice * item.quantity;
    return sum + (itemSubtotal * (item.discount / 100));
  }, 0);
  
  const grandTotal = subtotal - discountTotal + taxTotal;
  
  const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
  const remainingBalance = grandTotal - totalPaid;
  
  const completeTransaction = async (): Promise<Transaction> => {
    if (cart.length === 0) {
      throw new Error('Cart is empty');
    }
    
    if (remainingBalance > 0) {
      throw new Error('Payment is incomplete');
    }
    
    const transactionId = `T${Date.now().toString().slice(-6)}`;
    const now = new Date().toISOString();
    
    const profit = cart.reduce((total, item) => {
      const itemProfit = (item.product.unitPrice - item.product.buyingPrice) * item.quantity;
      const discountAmount = (item.product.unitPrice * item.quantity) * (item.discount / 100);
      return total + (itemProfit - discountAmount);
    }, 0);
    
    const transaction: Transaction = {
      id: transactionId,
      dateTime: now,
      cashierId: 'EMP01',
      cashierName: 'Jane Smith',
      // Removed customerId
      // Removed customerName
      payments: [...payments],
      items: [...cart],
      subtotal,
      taxTotal,
      discountTotal,
      total: grandTotal,
      profit,
      status: 'completed',
      receiptNumber: `R${Date.now().toString().slice(-8)}`
    };
    
    try {
      await save<Transaction>('transactions', transaction);
      
      for (const item of cart) {
        const product = {...item.product};
        product.stockQuantity -= item.quantity;
        await save<Product>('products', product);
      }
      
      // Removed customer update logic
      
      await refreshProducts();
      await refreshTransactions(); // Refresh transactions after saving
      
      setLastTransaction(transaction);
      
      clearCart();
      // Removed setCurrentCustomer(null) call
      clearPayments();
      
      toast({
        title: 'Transaction complete',
        description: `Transaction #${transactionId} completed successfully`,
      });
      
      return transaction;
    } catch (error) {
      console.error('Error completing transaction:', error);
      toast({
        title: 'Error',
        description: 'Failed to complete transaction',
        variant: 'destructive'
      });
      throw error;
    }
  };
  
  return (
    <AppContext.Provider
      value={{
        cart,
        addToCart,
        updateCartItem,
        removeFromCart,
        clearCart,
        
        subtotal,
        taxTotal,
        discountTotal,
        grandTotal,
        
        // Removed currentCustomer
        // Removed setCurrentCustomer
        
        payments,
        addPayment,
        removePayment,
        clearPayments,
        remainingBalance,
        
        completeTransaction,
        lastTransaction,
        transactions,
        
        products,
        refreshProducts,
        saveProduct,

        expenses,
        refreshExpenses,
        saveExpense,
        deleteExpense,

        // Removed customers
        // Removed refreshCustomers

        isLoading
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
