
import React from 'react';
import { Transaction } from '../types/models';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Printer, Download } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface ReceiptPreviewProps {
  transaction: Transaction;
}

const ReceiptPreview: React.FC<ReceiptPreviewProps> = ({ transaction }) => {
  const printReceipt = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;
    
    const html = `
      <html>
        <head>
          <title>Receipt #${transaction.receiptNumber}</title>
          <style>
            body {
              font-family: monospace;
              margin: 0;
              padding: 20px;
              width: 300px;
            }
            .header {
              text-align: center;
              margin-bottom: 10px;
            }
            .store-name {
              font-size: 18px;
              font-weight: bold;
            }
            .receipt-info {
              margin-bottom: 10px;
            }
            .items {
              margin: 10px 0;
            }
            .item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 5px;
            }
            .totals {
              margin-top: 10px;
              border-top: 1px dashed #000;
              padding-top: 10px;
            }
            .total-line {
              display: flex;
              justify-content: space-between;
            }
            .footer {
              margin-top: 20px;
              text-align: center;
              font-size: 12px;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="store-name">PharmacyPlus</div>
            <div>123 Health Street, Medicville</div>
            <div>555-PHARMACY</div>
          </div>
          
          <div class="receipt-info">
            <div>Receipt #: ${transaction.receiptNumber}</div>
            <div>Date: ${new Date(transaction.dateTime).toLocaleString()}</div>
            <div>Cashier: ${transaction.cashierName}</div>
            {/* Removed Customer Name display from print template */}
          </div>
          
          <div class="items">
            ${transaction.items.map(item => `
              <div class="item">
                <div>
                  ${item.quantity} x ${item.product.name}
                  ${item.discount > 0 ? ` (-${item.discount}%)` : ''}
                </div>
                <div>K${(item.product.unitPrice * item.quantity).toFixed(2)}</div>
              </div>
            `).join('')}
          </div>

          <div class="totals">
            <div class="total-line">
              <div>Subtotal:</div>
              <div>K${transaction.subtotal.toFixed(2)}</div>
            </div>
            ${transaction.discountTotal > 0 ? `
              <div class="total-line">
                <div>Discount:</div>
                <div>-K${transaction.discountTotal.toFixed(2)}</div>
              </div>
            ` : ''}
            <div class="total-line">
              <div>Tax:</div>
              <div>K${transaction.taxTotal.toFixed(2)}</div>
            </div>
            <div class="total-line" style="font-weight: bold; margin-top: 5px;">
              <div>Total:</div>
              <div>K${transaction.total.toFixed(2)}</div>
            </div>
          </div>

          <div class="payments">
            ${transaction.payments.map(payment => `
              <div class="total-line">
                <div>${payment.method.charAt(0).toUpperCase() + payment.method.slice(1)}:</div>
                <div>K${payment.amount.toFixed(2)}</div>
              </div>
            `).join('')}
          </div>
          
          <div class="footer">
            <div>Thank you for choosing PharmacyPlus! Get well soon!</div>
            <div>www.pharmacyplus.com</div>
          </div>
        </body>
      </html>
    `;
    
    printWindow.document.write(html);
    printWindow.document.close();
    printWindow.print();
  };

  const downloadReceiptPDF = () => {
    // In a real application, this would generate a PDF file
    alert('In a real app, this would download a PDF of the receipt.');
  };

  return (
    <div className="bg-white p-4 rounded-lg border max-w-md mx-auto">
      <div className="text-center mb-4">
        <h2 className="text-xl font-bold">PharmacyPlus</h2>
        <p className="text-sm text-muted-foreground">123 Health Street, Medicville</p>
        <p className="text-sm text-muted-foreground">555-PHARMACY</p>
      </div>
      
      <div className="mb-4">
        <p className="text-sm">Receipt #: {transaction.receiptNumber}</p>
        <p className="text-sm">Date: {new Date(transaction.dateTime).toLocaleString()}</p>
        <p className="text-sm">Cashier: {transaction.cashierName}</p>
        {/* Removed Customer Name display from component JSX */}
      </div>
      
      <Separator />
      
      <ScrollArea className="h-48 my-4">
        <div className="space-y-2">
          {transaction.items.map((item, index) => (
            <div key={index} className="flex justify-between text-sm">
              <div>
                {item.quantity} x {item.product.name}
                {item.discount > 0 && <span className="text-pharmacy-danger"> (-{item.discount}%)</span>}
              </div>
              <div>{formatCurrency(item.product.unitPrice * item.quantity)}</div>
            </div>
          ))}
        </div>
      </ScrollArea>

      <Separator />

      <div className="mt-4 space-y-1">
        <div className="flex justify-between text-sm">
          <span>Subtotal:</span>
          <span>{formatCurrency(transaction.subtotal)}</span>
        </div>
        {transaction.discountTotal > 0 && (
          <div className="flex justify-between text-sm">
            <span>Discount:</span>
            <span className="text-pharmacy-danger">-{formatCurrency(transaction.discountTotal)}</span>
          </div>
        )}
        <div className="flex justify-between text-sm">
          <span>Tax:</span>
          <span>{formatCurrency(transaction.taxTotal)}</span>
        </div>
        <div className="flex justify-between font-bold mt-2">
          <span>Total:</span>
          <span>{formatCurrency(transaction.total)}</span>
        </div>
      </div>

      <div className="mt-2 space-y-1">
        {transaction.payments.map((payment, index) => (
          <div key={index} className="flex justify-between text-sm">
            <span>{payment.method.charAt(0).toUpperCase() + payment.method.slice(1)}:</span>
            <span>{formatCurrency(payment.amount)}</span>
          </div>
        ))}
      </div>
      
      <div className="text-center mt-6 text-sm text-muted-foreground">
        <p>Thank you for choosing PharmacyPlus! Get well soon!</p>
        <p>www.pharmacyplus.com</p>
      </div>
      
      <div className="mt-4 flex space-x-2">
        <Button onClick={printReceipt} className="flex-1">
          <Printer className="w-4 h-4 mr-2" />
          Print
        </Button>
        <Button onClick={downloadReceiptPDF} variant="outline" className="flex-1">
          <Download className="w-4 h-4 mr-2" />
          Download
        </Button>
      </div>
    </div>
  );
};

export default ReceiptPreview;
