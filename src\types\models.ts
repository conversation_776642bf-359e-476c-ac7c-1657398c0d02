export interface Product {
  id: number;
  name: string;
  category: string;
  supplierName: string; // Renamed and made required
  buyingPrice: number;
  unitPrice: number;
  taxRate: number;
  stockQuantity: number;
  expiryDate: string;
  batchNumber: string;
  image?: string;
  lowStockThreshold?: number; // Added low stock threshold
}

export interface CartItem {
  product: Product;
  quantity: number;
  discount: number;
  override?: {
    price?: number;
    reason?: string;
  };
}

// Removed Customer interface

export interface Payment {
  // Changed 'card' to 'bank'
  method: 'cash' | 'bank' | 'mobile' | 'loyalty' | 'split'; 
  amount: number;
  // Removed cardDetails, bank payments will use reference
  mobileProvider?: 'TNM' | 'Airtel'; // Added optional mobile provider
  reference?: string; // Keep reference for bank/mobile
}

export interface Transaction {
  id: string;
  dateTime: string;
  cashierId: string;
  cashierName: string;
  // Removed customerId
  // Removed customerName
  payments: Payment[];
  items: CartItem[];
  subtotal: number;
  taxTotal: number;
  discountTotal: number;
  total: number;
  profit: number;
  status: 'completed' | 'voided' | 'returned';
  receiptNumber: string;
  notes?: string;
}

export interface Expense {
  id: string;
  description: string;
  amount: number;
  category: 'rent' | 'utilities' | 'supplies' | 'salaries' | 'marketing' | 'maintenance' | 'other';
  date: string;
  createdBy: string;
  createdByName: string;
  notes?: string;
}

export interface User {
  id: string;
  name: string;
  role: 'admin' | 'manager' | 'cashier' | 'pharmacist';
  email?: string;
  pin: string;
}

// Changed 'card' to 'bank'
export type PaymentMethod = 'cash' | 'bank' | 'mobile' | 'loyalty' | 'split';
