import React, { useState } from 'react'; // Import useState
import { useAppContext } from '../contexts/AppContext';
import CartItemRow from './CartItemRow';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { User, CreditCard, DollarSign, X, Wallet } from 'lucide-react'; // Added Wallet icon
import { toast } from '@/hooks/use-toast';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
// Removed CustomerSearch import
import PaymentForm from './PaymentForm';
import { Input } from '@/components/ui/input'; // Import Input
import { Label } from '@/components/ui/label'; // Import Label
import { PaymentMethod } from '@/types/models'; // Import PaymentMethod type
import { formatCurrency } from '@/lib/utils';

const Cart: React.FC = () => {
  const {
    cart,
    clearCart,
    subtotal,
    taxTotal,
    discountTotal,
    grandTotal,
    // Removed currentCustomer
    // Removed setCurrentCustomer
    payments,
    addPayment, // Add addPayment from context
    remainingBalance,
    completeTransaction
  } = useAppContext();
  const [cashTendered, setCashTendered] = useState('');

  const handleCheckout = async () => {
    if (cart.length === 0) {
      toast({
        title: 'Empty Cart',
        description: 'Please add items to the cart before checkout',
        variant: 'destructive'
      });
      return;
    }

    if (remainingBalance > 0) {
      toast({
        title: 'Payment Required',
        description: `Please complete payment of $${remainingBalance.toFixed(2)}`,
        variant: 'destructive'
      });
      return;
    }

    try {
      await completeTransaction();
      toast({
        title: 'Success',
        description: 'Transaction completed successfully'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to complete transaction',
        variant: 'destructive'
      });
    }
  };

  const handleAddCashPayment = () => {
    const amount = parseFloat(cashTendered);
    if (!isNaN(amount) && amount > 0) {
        addPayment({ method: 'cash', amount });
        setCashTendered(''); // Clear input after adding
    } else {
         toast({ title: "Invalid Amount", description: "Please enter a valid cash amount.", variant: "destructive" });
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="py-3 px-4 flex flex-row justify-between items-center space-y-0">
        <CardTitle className="text-lg">Shopping Cart</CardTitle>

        {/* Removed Customer selection Sheet */}
        <div className="flex items-center gap-2">
          {cart.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              className="h-8 text-muted-foreground hover:text-destructive"
              onClick={clearCart}
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>
      </CardHeader>

      {/* Removed overflow-hidden */}
      <CardContent className="flex-1 p-0"> 
        {cart.length === 0 ? (
          <div className="h-full flex items-center justify-center p-4">
            <div className="text-center">
              <p className="text-muted-foreground">Cart is empty</p>
              <p className="text-sm text-muted-foreground mt-1">
                Search for products to add to the cart
              </p>
            </div>
          </div>
        ) : (
          // Removed px-4 from ScrollArea
          <ScrollArea className="h-full"> 
            {/* Added px-2 to inner div */}
            <div className="py-2 px-2"> 
              {cart.map((item, index) => (
                <CartItemRow key={`${item.product.id}-${index}`} item={item} index={index} />
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>

      <CardFooter className="flex-col p-4 pt-2 space-y-3">
        <div className="w-full">
          {/* Totals Section */}
          <div className="flex justify-between py-1">
            <span className="text-muted-foreground">Subtotal:</span>
            <span>{formatCurrency(subtotal)}</span>
          </div>
          {discountTotal > 0 && (
            <div className="flex justify-between py-1">
              <span className="text-muted-foreground">Discount:</span>
              <span className="text-pharmacy-danger">-{formatCurrency(discountTotal)}</span>
            </div>
          )}
          <div className="flex justify-between py-1">
            <span className="text-muted-foreground">Tax:</span>
            <span>{formatCurrency(taxTotal)}</span>
          </div>
          <Separator className="my-2" />
          <div className="flex justify-between font-medium text-lg"> {/* Made Total larger */}
            <span>Total:</span>
            <span>{formatCurrency(grandTotal)}</span>
          </div>

          {/* Default Cash Payment Input */}
          {grandTotal > 0 && ( // Only show if there's a total
            <div className="flex items-end gap-2 pt-3">
               <div className="flex-1">
                  <Label htmlFor="cash-tendered" className="text-xs text-muted-foreground">Cash Tendered</Label>
                  <Input
                      id="cash-tendered"
                      type="number"
                      placeholder="Enter amount"
                      value={cashTendered}
                      onChange={(e) => setCashTendered(e.target.value)}
                      className="h-9"
                      min="0"
                      step="0.01"
                      onKeyDown={(e) => { if (e.key === 'Enter') handleAddCashPayment(); }} // Add payment on Enter key
                  />
               </div>
               <Button
                  size="sm"
                  variant="outline"
                  className="h-9"
                  onClick={handleAddCashPayment}
                  disabled={!cashTendered || parseFloat(cashTendered) <= 0}
               >
                  Add Cash
               </Button>
            </div>
          )}

          {/* Display Applied Payments & Balance/Change */}
          {payments.length > 0 && (
            <div className="mt-3 border-t pt-3 space-y-1"> {/* Added space-y-1 */}
              <p className="text-xs font-medium text-muted-foreground mb-1">Applied Payments:</p>
              {payments.map((payment, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span className="text-muted-foreground flex items-center capitalize">
                    {payment.method === 'cash' && <DollarSign className="h-3 w-3 mr-1.5" />}
                    {/* Changed 'card' to 'bank' */}
                    {payment.method === 'bank' && <CreditCard className="h-3 w-3 mr-1.5" />}
                    {/* Add other icons for other payment methods if needed */}
                    {payment.method}
                  </span>
                  <span>{formatCurrency(payment.amount)}</span>
                </div>
              ))}
              <Separator className="my-2" />
              <div className="flex justify-between text-sm font-semibold"> {/* Made font-semibold */}
                <span className={remainingBalance > 0 ? "text-pharmacy-danger" : "text-pharmacy-success"}>
                  {remainingBalance > 0 ? "Balance Due:" : "Change:"}
                </span>
                <span className={remainingBalance > 0 ? "text-pharmacy-danger" : "text-pharmacy-success"}>
                  {formatCurrency(Math.abs(remainingBalance))}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-2 w-full pt-2"> {/* Added padding-top */}
          <Sheet>
            <SheetTrigger asChild>
              <Button className="w-full" variant="outline" disabled={grandTotal <= 0}> {/* Disable if no total */}
                <Wallet className="h-4 w-4 mr-2" />
                Add Other Payment
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Add Other Payment</SheetTitle> {/* Updated Title */}
              </SheetHeader>
              {/* Pass grandTotal or remainingBalance to PaymentForm if needed */}
              <PaymentForm />
            </SheetContent>
          </Sheet>

          <Button
            className="w-full"
            onClick={handleCheckout}
            disabled={cart.length === 0 || remainingBalance > 0}
          >
            Checkout
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default Cart;
